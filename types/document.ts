/**
 * Document Management Types
 * TypeScript interfaces for document entities and API responses
 */

export interface Document {
  document_id: string;
  document_name: string;
  document_description?: string;
  document_category: string;
  file_name: string;
  file_size: number;
  file_type: string;
  file_url?: string;
  expiry_date?: string;
  upload_date: string;
  uploaded_by: string;
  employee_id?: string;
  company_id: string;
  is_expired?: boolean;
  days_until_expiry?: number;
}

export interface DocumentUploadRequest {
  document_name: string;
  document_description?: string;
  document_category: string;
  file: File;
  expiry_date?: string;
  employee_id?: string;
}

export interface DocumentUploadResponse {
  success: boolean;
  message: string;
  document?: Document;
  error?: string;
}

export interface DocumentListResponse {
  success: boolean;
  documents: Document[];
  pagination?: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  };
  message?: string;
  error?: string;
}

export interface DocumentDetailsResponse {
  success: boolean;
  document?: Document;
  message?: string;
  error?: string;
}

export interface ExpiringDocumentsResponse {
  success: boolean;
  documents: Document[];
  total_expiring: number;
  message?: string;
  error?: string;
}

export interface DocumentFilters {
  category?: string;
  employee_id?: string;
  search?: string;
  expiry_status?: 'all' | 'expiring' | 'expired' | 'valid';
  date_from?: string;
  date_to?: string;
  page?: number;
  limit?: number;
}

export interface DocumentCategory {
  value: string;
  label: string;
  description?: string;
}

export const DOCUMENT_CATEGORIES: DocumentCategory[] = [
  { value: 'resume', label: 'Resume/CV', description: 'Employee resumes and curriculum vitae' },
  { value: 'contract', label: 'Contract', description: 'Employment contracts and agreements' },
  { value: 'id_document', label: 'ID Document', description: 'Identity documents and passports' },
  { value: 'certificate', label: 'Certificate', description: 'Professional certificates and qualifications' },
  { value: 'policy', label: 'Policy', description: 'Company policies and procedures' },
  { value: 'training', label: 'Training', description: 'Training materials and records' },
  { value: 'performance', label: 'Performance', description: 'Performance reviews and evaluations' },
  { value: 'medical', label: 'Medical', description: 'Medical certificates and health records' },
  { value: 'legal', label: 'Legal', description: 'Legal documents and compliance records' },
  { value: 'other', label: 'Other', description: 'Other miscellaneous documents' }
];

export const SUPPORTED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'image/jpeg',
  'image/png',
  'image/gif',
  'text/plain'
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export interface DocumentStats {
  total_documents: number;
  documents_by_category: Record<string, number>;
  expiring_soon: number;
  expired: number;
  total_size: number;
}
