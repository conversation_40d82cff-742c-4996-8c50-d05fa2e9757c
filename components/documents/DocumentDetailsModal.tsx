'use client';

import React, { useState, useEffect } from 'react';
import { Document } from '@/types/document';
import { getDocumentById, downloadDocument, deleteDocument } from '@/lib/documents';
import { formatFileSize, getFileIcon } from '@/lib/documents';
import { X, Download, Trash2, Calendar, User, FileText, Tag, Clock, AlertTriangle } from 'lucide-react';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import toast from 'react-hot-toast';

interface DocumentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
  onDelete?: () => void;
}

const DocumentDetailsModal: React.FC<DocumentDetailsModalProps> = ({
  isOpen,
  onClose,
  documentId,
  onDelete
}) => {
  const [document, setDocument] = useState<Document | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (isOpen && documentId) {
      loadDocument();
    }
  }, [isOpen, documentId]);

  const loadDocument = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await getDocumentById(documentId);
      
      if (response.success && response.document) {
        setDocument(response.document);
      } else {
        setError(response.error || 'Failed to load document details');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load document details');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    if (!document) return;

    try {
      await downloadDocument(document.document_id, document.file_name);
      toast.success('Document downloaded successfully');
    } catch (err: any) {
      toast.error(err.message || 'Failed to download document');
    }
  };

  const handleDelete = async () => {
    if (!document) return;

    const confirmed = confirm(`Are you sure you want to delete "${document.document_name}"? This action cannot be undone.`);
    if (!confirmed) return;

    try {
      setIsDeleting(true);
      const response = await deleteDocument(document.document_id);
      
      if (response.success) {
        toast.success('Document deleted successfully');
        onDelete?.();
        onClose();
      } else {
        toast.error('Failed to delete document');
      }
    } catch (err: any) {
      toast.error(err.message || 'Failed to delete document');
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'resume': 'bg-blue-100 text-blue-800',
      'contract': 'bg-green-100 text-green-800',
      'id_document': 'bg-purple-100 text-purple-800',
      'certificate': 'bg-yellow-100 text-yellow-800',
      'policy': 'bg-gray-100 text-gray-800',
      'training': 'bg-indigo-100 text-indigo-800',
      'performance': 'bg-pink-100 text-pink-800',
      'medical': 'bg-red-100 text-red-800',
      'legal': 'bg-orange-100 text-orange-800',
      'other': 'bg-gray-100 text-gray-800'
    };
    return colors[category] || colors['other'];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" 
          aria-hidden="true"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Document Details
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="bg-white px-6 py-6">
            {loading ? (
              <div className="py-12">
                <LoadingSpinner size="lg" message="Loading document details..." />
              </div>
            ) : error ? (
              <div className="py-12 text-center">
                <AlertTriangle className="mx-auto h-12 w-12 text-red-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Document</h3>
                <p className="text-gray-500">{error}</p>
              </div>
            ) : document ? (
              <div className="space-y-6">
                {/* Document Header */}
                <div className="flex items-start space-x-4">
                  <div className="text-4xl">
                    {getFileIcon(document.file_type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      {document.document_name}
                    </h2>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>{document.file_name}</span>
                      <span>•</span>
                      <span>{formatFileSize(document.file_size)}</span>
                    </div>
                  </div>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(document.document_category)}`}>
                    {document.document_category.replace('_', ' ').toUpperCase()}
                  </span>
                </div>

                {/* Expiry Warning */}
                {(document.is_expired || (document.days_until_expiry !== undefined && document.days_until_expiry <= 30)) && (
                  <div className={`p-4 rounded-md flex items-center space-x-3 ${
                    document.is_expired ? 'bg-red-50 text-red-700' : 'bg-yellow-50 text-yellow-700'
                  }`}>
                    <AlertTriangle className="h-5 w-5" />
                    <div>
                      <p className="font-medium">
                        {document.is_expired ? 'Document Expired' : 'Document Expiring Soon'}
                      </p>
                      <p className="text-sm">
                        {document.is_expired 
                          ? 'This document has expired and may need to be renewed.'
                          : `This document expires in ${document.days_until_expiry} days.`
                        }
                      </p>
                    </div>
                  </div>
                )}

                {/* Description */}
                {document.document_description && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 mb-2">Description</h3>
                    <p className="text-gray-700">{document.document_description}</p>
                  </div>
                )}

                {/* Metadata */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Upload Date</p>
                        <p className="text-sm text-gray-600">{formatDate(document.upload_date)}</p>
                      </div>
                    </div>

                    {document.expiry_date && (
                      <div className="flex items-center space-x-3">
                        <Clock className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Expiry Date</p>
                          <p className="text-sm text-gray-600">{formatDate(document.expiry_date)}</p>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">File Type</p>
                        <p className="text-sm text-gray-600">{document.file_type}</p>
                      </div>
                    </div>

                    {document.employee_id && (
                      <div className="flex items-center space-x-3">
                        <User className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Employee Document</p>
                          <p className="text-sm text-gray-600">Assigned to specific employee</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex justify-between items-center pt-6 border-t border-gray-200">
                  <button
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {isDeleting ? 'Deleting...' : 'Delete Document'}
                  </button>

                  <button
                    onClick={handleDownload}
                    className="inline-flex items-center px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </button>
                </div>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentDetailsModal;
