"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import {
  Document,
  DocumentFilters as DocumentFiltersType,
} from "@/types/document";
import {
  getDocuments,
  getExpiringDocuments,
  downloadDocument,
  deleteDocument,
} from "@/lib/documents";
import { getEmployees } from "@/lib/employee";
import toast from "react-hot-toast";
import DocumentList from "./DocumentList";
import DocumentFilters from "./DocumentFilters";
import DocumentUploadModal from "./DocumentUploadModal";
import DocumentDetailsModal from "./DocumentDetailsModal";
import DeleteConfirmationModal from "./DeleteConfirmationModal";
import DashboardCard from "@/components/ui/DashboardCard";
import DashboardStats from "@/components/ui/DashboardStats";
import {
  Plus,
  AlertTriangle,
  FileText,
  Clock,
  Users,
  Download,
} from "lucide-react";

const DocumentManagement: React.FC = () => {
  const { companies } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [expiringDocuments, setExpiringDocuments] = useState<Document[]>([]);
  const [employees, setEmployees] = useState<
    Array<{ employee_id: string; full_name: string }>
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(
    null
  );
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState(false);

  const [filters, setFilters] = useState<DocumentFiltersType>({
    page: 1,
    limit: 12,
  });

  const [pagination, setPagination] = useState<
    | {
        current_page: number;
        total_pages: number;
        total_items: number;
        items_per_page: number;
      }
    | undefined
  >();

  const [stats, setStats] = useState({
    total: 0,
    expiring: 0,
    expired: 0,
    categories: 0,
  });

  // Load initial data
  useEffect(() => {
    loadDocuments();
    loadExpiringDocuments();
    loadEmployees();
  }, [companies]);

  // Reload documents when filters change
  useEffect(() => {
    loadDocuments();
  }, [filters]);

  const loadDocuments = async () => {
    if (!companies || companies.length === 0) return;

    try {
      setLoading(true);
      setError("");

      const response = await getDocuments(filters);

      if (response.success) {
        setDocuments(response.documents);
        setPagination(response.pagination);

        // Update stats
        setStats((prev) => ({
          ...prev,
          total: response.pagination?.total_items || response.documents.length,
        }));
      } else {
        setError(response.error || "Failed to load documents");
      }
    } catch (err: any) {
      setError(err.message || "Failed to load documents");
    } finally {
      setLoading(false);
    }
  };

  const loadExpiringDocuments = async () => {
    if (!companies || companies.length === 0) return;

    try {
      const response = await getExpiringDocuments(30);

      if (response.success) {
        setExpiringDocuments(response.documents);
        setStats((prev) => ({
          ...prev,
          expiring: response.documents.filter((d) => !d.is_expired).length,
          expired: response.documents.filter((d) => d.is_expired).length,
        }));
      }
    } catch (err: any) {
      console.error("Failed to load expiring documents:", err);
    }
  };

  const loadEmployees = async () => {
    if (!companies || companies.length === 0) return;

    try {
      const companyId = companies[0].company_id;
      const response = await getEmployees(companyId);

      if (response.success && response.extend?.employees) {
        setEmployees(
          response.extend.employees.map((emp) => ({
            employee_id: emp.employee_id,
            full_name: emp.full_name,
          }))
        );
      }
    } catch (err: any) {
      console.error("Failed to load employees:", err);
    }
  };

  const handleDownload = async (document: Document) => {
    try {
      await downloadDocument(document.document_id, document.file_name);
      toast.success("Document downloaded successfully");
    } catch (err: any) {
      toast.error(err.message || "Failed to download document");
    }
  };

  const handleView = (document: Document) => {
    setSelectedDocumentId(document.document_id);
  };

  const handleDelete = (document: Document) => {
    setDocumentToDelete(document);
  };

  const handleConfirmDelete = async () => {
    if (!documentToDelete) return;

    try {
      setIsDeleting(true);
      const response = await deleteDocument(documentToDelete.document_id);

      if (response.success) {
        toast.success("Document deleted successfully");
        setDocumentToDelete(null);
        loadDocuments();
        loadExpiringDocuments();
      } else {
        toast.error("Failed to delete document");
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to delete document");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setDocumentToDelete(null);
  };

  const handleUploadSuccess = () => {
    loadDocuments();
    loadExpiringDocuments();
    toast.success("Document uploaded successfully");
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleFiltersChange = (newFilters: DocumentFiltersType) => {
    setFilters(newFilters);
  };

  const statsData = [
    {
      title: "Total Documents",
      value: stats.total.toString(),
      change: "",
      changeType: "neutral" as const,
      icon: <FileText className="h-5 w-5" />,
    },
    {
      title: "Expiring Soon",
      value: stats.expiring.toString(),
      change: "",
      changeType:
        stats.expiring > 0 ? ("negative" as const) : ("neutral" as const),
      icon: <Clock className="h-5 w-5" />,
    },
    {
      title: "Expired",
      value: stats.expired.toString(),
      change: "",
      changeType:
        stats.expired > 0 ? ("negative" as const) : ("neutral" as const),
      icon: <AlertTriangle className="h-5 w-5" />,
    },
    {
      title: "Employees",
      value: employees.length.toString(),
      change: "",
      changeType: "neutral" as const,
      icon: <Users className="h-5 w-5" />,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Document Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage and organize your company documents
          </p>
        </div>

        <button
          onClick={() => setIsUploadModalOpen(true)}
          className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          Upload Document
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            loading={loading}
          />
        ))}
      </div>

      {/* Expiring Documents Alert */}
      {expiringDocuments.length > 0 && (
        <DashboardCard>
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-yellow-500" />
              <h3 className="text-lg font-semibold text-gray-900">
                Documents Requiring Attention
              </h3>
            </div>

            <div className="space-y-2">
              {expiringDocuments.slice(0, 5).map((doc) => (
                <div
                  key={doc.document_id}
                  className="flex items-center justify-between p-3 bg-yellow-50 rounded-md"
                >
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">
                      {doc.document_name}
                    </p>
                    <p className="text-sm text-gray-600">
                      {doc.is_expired
                        ? "Expired"
                        : `Expires in ${doc.days_until_expiry} days`}
                    </p>
                  </div>
                  <button
                    onClick={() => handleDownload(doc)}
                    className="text-primary hover:text-primary-dark"
                  >
                    <Download className="h-5 w-5" />
                  </button>
                </div>
              ))}
            </div>

            {expiringDocuments.length > 5 && (
              <p className="text-sm text-gray-500 mt-3">
                And {expiringDocuments.length - 5} more documents...
              </p>
            )}
          </div>
        </DashboardCard>
      )}

      {/* Filters */}
      <DocumentFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        employees={employees}
      />

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-red-400 mt-0.5" />
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-red-800">
                Unable to Load Documents
              </h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              <button
                onClick={loadDocuments}
                className="mt-2 text-sm font-medium text-red-800 hover:text-red-900 underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Document List */}
      <DocumentList
        documents={documents}
        loading={loading}
        onDownload={handleDownload}
        onView={handleView}
        onDelete={handleDelete}
        showEmployeeInfo={true}
        pagination={pagination}
        onPageChange={handlePageChange}
      />

      {/* Upload Modal */}
      <DocumentUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onSuccess={handleUploadSuccess}
        employees={employees}
      />

      {/* Document Details Modal */}
      {selectedDocumentId && (
        <DocumentDetailsModal
          isOpen={!!selectedDocumentId}
          onClose={() => setSelectedDocumentId(null)}
          documentId={selectedDocumentId}
          onDelete={() => {
            loadDocuments();
            loadExpiringDocuments();
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={!!documentToDelete}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        document={documentToDelete}
        isDeleting={isDeleting}
      />
    </div>
  );
};

export default DocumentManagement;
