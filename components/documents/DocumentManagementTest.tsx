'use client';

import React from 'react';
import { Document } from '@/types/document';
import DocumentCard from './DocumentCard';
import DocumentList from './DocumentList';
import DocumentFilters from './DocumentFilters';

// Test component to verify document management components work correctly
const DocumentManagementTest: React.FC = () => {
  // Mock data for testing
  const mockDocuments: Document[] = [
    {
      document_id: '1',
      document_name: 'Employee Handbook 2024',
      document_description: 'Updated company policies and procedures',
      document_category: 'policy',
      file_name: 'employee-handbook-2024.pdf',
      file_size: 2048576, // 2MB
      file_type: 'application/pdf',
      upload_date: '2024-01-15T10:30:00Z',
      uploaded_by: '<EMAIL>',
      company_id: 'company-1',
      expiry_date: '2025-01-15T00:00:00Z',
      days_until_expiry: 45
    },
    {
      document_id: '2',
      document_name: '<PERSON>',
      document_description: 'Latest resume for <PERSON>',
      document_category: 'resume',
      file_name: 'john-doe-resume.pdf',
      file_size: 512000, // 500KB
      file_type: 'application/pdf',
      upload_date: '2024-02-01T14:20:00Z',
      uploaded_by: '<EMAIL>',
      employee_id: 'emp-123',
      company_id: 'company-1'
    },
    {
      document_id: '3',
      document_name: 'Expired Certificate',
      document_description: 'This certificate has expired',
      document_category: 'certificate',
      file_name: 'expired-cert.pdf',
      file_size: 1024000, // 1MB
      file_type: 'application/pdf',
      upload_date: '2023-01-01T00:00:00Z',
      uploaded_by: '<EMAIL>',
      company_id: 'company-1',
      expiry_date: '2024-01-01T00:00:00Z',
      is_expired: true,
      days_until_expiry: -30
    }
  ];

  const mockEmployees = [
    { employee_id: 'emp-123', full_name: 'John Doe' },
    { employee_id: 'emp-456', full_name: 'Jane Smith' }
  ];

  const handleDownload = (document: Document) => {
    console.log('Download document:', document.document_name);
    alert(`Downloading: ${document.document_name}`);
  };

  const handleView = (document: Document) => {
    console.log('View document:', document.document_name);
    alert(`Viewing: ${document.document_name}`);
  };

  const handleDelete = (document: Document) => {
    console.log('Delete document:', document.document_name);
    if (confirm(`Delete ${document.document_name}?`)) {
      alert(`Deleted: ${document.document_name}`);
    }
  };

  const handleFiltersChange = (filters: any) => {
    console.log('Filters changed:', filters);
  };

  return (
    <div className="space-y-8 p-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Document Management Test
        </h1>
        <p className="text-gray-600">
          This page tests the document management components with mock data.
        </p>
      </div>

      {/* Test DocumentCard */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Document Cards</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockDocuments.map((doc) => (
            <DocumentCard
              key={doc.document_id}
              document={doc}
              onDownload={handleDownload}
              onView={handleView}
              onDelete={handleDelete}
              showEmployeeInfo={true}
            />
          ))}
        </div>
      </div>

      {/* Test DocumentFilters */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Document Filters</h2>
        <DocumentFilters
          filters={{ page: 1, limit: 12 }}
          onFiltersChange={handleFiltersChange}
          employees={mockEmployees}
        />
      </div>

      {/* Test DocumentList */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Document List</h2>
        <DocumentList
          documents={mockDocuments}
          loading={false}
          onDownload={handleDownload}
          onView={handleView}
          onDelete={handleDelete}
          showEmployeeInfo={true}
          pagination={{
            current_page: 1,
            total_pages: 1,
            total_items: mockDocuments.length,
            items_per_page: 12
          }}
          onPageChange={(page) => console.log('Page changed:', page)}
        />
      </div>

      {/* Test Loading State */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Loading State</h2>
        <DocumentList
          documents={[]}
          loading={true}
          onDownload={handleDownload}
          onView={handleView}
          onDelete={handleDelete}
        />
      </div>

      {/* Test Empty State */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Empty State</h2>
        <DocumentList
          documents={[]}
          loading={false}
          onDownload={handleDownload}
          onView={handleView}
          onDelete={handleDelete}
        />
      </div>
    </div>
  );
};

export default DocumentManagementTest;
