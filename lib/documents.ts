/**
 * Document Management API Service
 * Handles all API calls for document management functionality
 */

import { apiGet, createApiUrl } from './api';
import { getAccessToken } from './auth';
import {
  Document,
  DocumentUploadRequest,
  DocumentUploadResponse,
  DocumentListResponse,
  DocumentDetailsResponse,
  ExpiringDocumentsResponse,
  DocumentFilters,
  DocumentStats
} from '@/types/document';

/**
 * Upload a document (general or employee-specific)
 */
export async function uploadDocument(data: DocumentUploadRequest): Promise<DocumentUploadResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  // Create FormData for file upload
  const formData = new FormData();
  formData.append('document_name', data.document_name);
  formData.append('document_category', data.document_category);
  formData.append('file', data.file);

  if (data.document_description) {
    formData.append('document_description', data.document_description);
  }

  if (data.expiry_date) {
    formData.append('expiry_date', data.expiry_date);
  }

  if (data.employee_id) {
    formData.append('employee_id', data.employee_id);
  }

  const url = createApiUrl('documents/upload');
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for FormData - browser will set it with boundary
    },
    body: formData
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || 'Failed to upload document');
  }

  return response.json();
}

/**
 * Get list of documents with optional filters
 */
export async function getDocuments(filters?: DocumentFilters): Promise<DocumentListResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  let endpoint = 'documents';
  const params = new URLSearchParams();

  if (filters) {
    if (filters.category) params.append('category', filters.category);
    if (filters.employee_id) params.append('employee_id', filters.employee_id);
    if (filters.search) params.append('search', filters.search);
    if (filters.expiry_status && filters.expiry_status !== 'all') {
      params.append('expiry_status', filters.expiry_status);
    }
    if (filters.date_from) params.append('date_from', filters.date_from);
    if (filters.date_to) params.append('date_to', filters.date_to);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
  }

  if (params.toString()) {
    endpoint += `?${params.toString()}`;
  }

  return await apiGet<DocumentListResponse>(endpoint, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
}

/**
 * Get document by ID
 */
export async function getDocumentById(documentId: string): Promise<DocumentDetailsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return await apiGet<DocumentDetailsResponse>(`documents/${documentId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
}

/**
 * Get expiring documents
 */
export async function getExpiringDocuments(daysAhead: number = 30): Promise<ExpiringDocumentsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return await apiGet<ExpiringDocumentsResponse>(`documents/expiring?days_ahead=${daysAhead}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
}

/**
 * Download document
 */
export async function downloadDocument(documentId: string, fileName?: string): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const url = createApiUrl(`documents/${documentId}/download`);
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  if (!response.ok) {
    throw new Error('Failed to download document');
  }

  const blob = await response.blob();
  const downloadUrl = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = downloadUrl;
  a.download = fileName || `document-${documentId}`;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(downloadUrl);
  document.body.removeChild(a);
}

/**
 * Delete document
 */
export async function deleteDocument(documentId: string): Promise<{ success: boolean; message: string }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const url = createApiUrl(`documents/${documentId}`);
  const response = await fetch(url, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || 'Failed to delete document');
  }

  return response.json();
}

/**
 * Validate file before upload
 */
export function validateFile(file: File, maxSize: number = 10 * 1024 * 1024): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size must be less than ${Math.round(maxSize / (1024 * 1024))}MB`
    };
  }

  // Check file type
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/gif',
    'text/plain'
  ];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'File type not supported. Please upload PDF, Word, Excel, Image, or Text files.'
    };
  }

  return { valid: true };
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get file icon based on file type
 */
export function getFileIcon(fileType: string): string {
  if (fileType.includes('pdf')) return '📄';
  if (fileType.includes('word')) return '📝';
  if (fileType.includes('excel') || fileType.includes('sheet')) return '📊';
  if (fileType.includes('image')) return '🖼️';
  if (fileType.includes('text')) return '📄';
  return '📎';
}
